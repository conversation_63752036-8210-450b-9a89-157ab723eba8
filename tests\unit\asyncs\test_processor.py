#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: AsyncComponentInitializer 单元测试

测试异步组件初始化器的功能.
"""

import unittest
from unittest.mock import Mock, patch

from miniboot.processor.asyncs import AsyncComponentInitializer
from miniboot.asyncs.properties import AsyncProperties, ThreadPoolConfig


class TestAsyncComponentInitializer(unittest.TestCase):
    """AsyncComponentInitializer 测试类"""

    def test_create_initializer(self):
        """测试创建初始化器"""
        initializer = AsyncComponentInitializer()

        self.assertIsNone(initializer._executor)
        self.assertIsNone(initializer._async_properties)
        self.assertFalse(initializer._initialized)

    def test_initialize_if_needed_first_time(self):
        """测试首次初始化"""
        initializer = AsyncComponentInitializer()

        # 模拟配置加载
        mock_properties = AsyncProperties(enabled=True, executor=ThreadPoolConfig(core_size=4, max_size=8))

        def mock_load_properties():
            initializer._async_properties = mock_properties

        with (
            patch.object(initializer, "_load_async_properties", side_effect=mock_load_properties),
            patch("miniboot.asyncs.executor.ThreadPoolTaskExecutor") as mock_executor_class,
        ):
                mock_executor = Mock()
                mock_executor_class.return_value = mock_executor

                result = initializer.initialize_if_needed()

                self.assertTrue(result)
                self.assertTrue(initializer._initialized)
                self.assertIs(initializer._async_properties, mock_properties)
                self.assertIsNotNone(initializer._executor)

    def test_initialize_if_needed_already_initialized(self):
        """测试重复初始化"""
        initializer = AsyncComponentInitializer()
        initializer._initialized = True

        result = initializer.initialize_if_needed()

        assert result is True
        # 不应该重新初始化

    def test_initialize_if_needed_disabled(self):
        """测试异步功能禁用时的初始化"""
        initializer = AsyncComponentInitializer()

        # 模拟禁用的配置
        mock_properties = AsyncProperties(enabled=False)

        with patch.object(initializer, "_load_async_properties", return_value=mock_properties):
            result = initializer.initialize_if_needed()

            assert result is False
            assert not initializer._initialized
            assert initializer._executor is None

    def test_initialize_if_needed_exception_handling(self):
        """测试初始化异常处理"""
        initializer = AsyncComponentInitializer()

        with (
            patch.object(initializer, "_load_async_properties", side_effect=Exception("Load error")),
            patch("miniboot.processor.asyncs.logger") as mock_logger,
        ):
            result = initializer.initialize_if_needed()

            assert result is False
            assert not initializer._initialized
            mock_logger.error.assert_called_once()

    def test_get_executor_before_initialization(self):
        """测试初始化前获取执行器"""
        initializer = AsyncComponentInitializer()

        executor = initializer.get_executor()

        assert executor is None

    def test_get_executor_after_initialization(self):
        """测试初始化后获取执行器"""
        initializer = AsyncComponentInitializer()

        # 模拟初始化
        mock_executor = Mock()
        initializer._executor = mock_executor
        initializer._initialized = True

        executor = initializer.get_executor()

        assert executor is mock_executor

    def test_get_async_properties_before_initialization(self):
        """测试初始化前获取异步属性"""
        initializer = AsyncComponentInitializer()

        properties = initializer.get_async_properties()

        assert properties is None

    def test_get_async_properties_after_initialization(self):
        """测试初始化后获取异步属性"""
        initializer = AsyncComponentInitializer()

        # 模拟初始化
        mock_properties = AsyncProperties()
        initializer._async_properties = mock_properties
        initializer._initialized = True

        properties = initializer.get_async_properties()

        assert properties is mock_properties

    def test_load_async_properties_success(self):
        """测试成功加载异步属性"""
        initializer = AsyncComponentInitializer()

        # 模拟环境配置
        mock_environment = Mock()
        mock_environment.get_property.side_effect = lambda key, default=None: {
            "miniboot.async.enabled": True,
            "miniboot.async.default_timeout": 30.0,
            "miniboot.async.executor.core_size": 4,
            "miniboot.async.executor.max_size": 8,
            "miniboot.async.executor.queue_capacity": 100,
            "miniboot.async.executor.keep_alive": 60,
            "miniboot.async.executor.thread_name_prefix": "test-",
            "miniboot.async.executor.allow_core_thread_timeout": False,
            "miniboot.async.executor.rejection_policy": "caller_runs",
        }.get(key, default)

        with patch.object(initializer, "_get_environment", return_value=mock_environment):
            initializer._load_async_properties()

        properties = initializer._async_properties
        assert isinstance(properties, AsyncProperties)
        assert properties.enabled is True
        assert properties.default_timeout == 30.0
        assert properties.executor.core_size == 4
        assert properties.executor.max_size == 8

    def test_load_async_properties_with_defaults(self):
        """测试使用默认值加载异步属性"""
        initializer = AsyncComponentInitializer()

        # 模拟没有环境配置
        with patch.object(initializer, "_get_environment", return_value=None):
            initializer._load_async_properties()

        properties = initializer._async_properties
        assert isinstance(properties, AsyncProperties)
        assert properties.enabled is True  # 默认值
        assert properties.default_timeout is None  # 默认值
        assert properties.executor.core_size == 2  # 默认值

    def test_load_async_properties_partial_config(self):
        """测试部分配置加载"""
        initializer = AsyncComponentInitializer()

        # 模拟环境配置，只提供部分配置
        mock_environment = Mock()
        mock_environment.get_property.side_effect = lambda key, default=None: {
            "miniboot.async.enabled": False,
            "miniboot.async.executor.core_size": 6,
        }.get(key, default)

        with patch.object(initializer, "_get_environment", return_value=mock_environment):
            initializer._load_async_properties()

        properties = initializer._async_properties
        assert properties.enabled is False
        assert properties.executor.core_size == 6
        assert properties.executor.max_size == 4  # 默认值

    def test_create_executor_success(self):
        """测试成功创建执行器"""
        initializer = AsyncComponentInitializer()

        # 设置模拟属性
        properties = AsyncProperties(enabled=True, executor=ThreadPoolConfig(core_size=4, max_size=8))
        initializer._async_properties = properties

        with patch("miniboot.asyncs.executor.ThreadPoolTaskExecutor") as mock_executor_class:
            mock_executor = Mock()
            mock_executor_class.return_value = mock_executor

            initializer._create_default_thread_pool()

            assert initializer._executor is mock_executor
            mock_executor_class.assert_called_once_with(properties.executor, "default")

    def test_create_executor_exception_handling(self):
        """测试创建执行器异常处理"""
        initializer = AsyncComponentInitializer()

        # 设置模拟属性
        properties = AsyncProperties()
        initializer._async_properties = properties

        with patch("miniboot.asyncs.executor.ThreadPoolTaskExecutor", side_effect=Exception("Create error")):
            with self.assertRaises(Exception) as cm:
                initializer._create_default_thread_pool()
            self.assertIn("Create error", str(cm.exception))

    def test_integration_full_initialization(self):
        """测试完整初始化流程"""
        initializer = AsyncComponentInitializer()

        # 模拟环境配置
        mock_environment = Mock()
        mock_environment.get_property.side_effect = lambda key, default=None: {
            "miniboot.async.enabled": True,
            "miniboot.async.default_timeout": 45.0,
            "miniboot.async.executor.core_size": 6,
            "miniboot.async.executor.max_size": 12,
            "miniboot.async.executor.rejection_policy": "discard",
        }.get(key, default)

        with (
            patch.object(initializer, "_get_environment", return_value=mock_environment),
            patch("miniboot.asyncs.executor.ThreadPoolTaskExecutor") as mock_executor_class,
        ):
                mock_executor = Mock()
                mock_executor_class.return_value = mock_executor

                # 执行初始化
                result = initializer.initialize_if_needed()

                # 验证结果
                assert result is True
                assert initializer._initialized is True
                assert initializer._async_properties.enabled is True
                assert initializer._async_properties.default_timeout == 45.0
                assert initializer._async_properties.executor.core_size == 6
                assert initializer._async_properties.executor.max_size == 12
                assert initializer._async_properties.executor.rejection_policy == "discard"
                assert initializer._executor is mock_executor

    def test_thread_safety(self):
        """测试线程安全性"""
        import threading

        initializer = AsyncComponentInitializer()
        results = []
        init_count = 0

        # 模拟配置加载
        mock_properties = AsyncProperties(enabled=True)

        def mock_load_properties():
            nonlocal init_count
            init_count += 1
            initializer._async_properties = mock_properties

        # 在测试开始前设置全局 mock
        with (
            patch.object(initializer, "_load_async_properties", side_effect=mock_load_properties),
            patch("miniboot.asyncs.executor.ThreadPoolTaskExecutor") as mock_executor_class,
        ):
            mock_executor = Mock()
            mock_executor_class.return_value = mock_executor

            def init_worker():
                result = initializer.initialize_if_needed()
                results.append(result)

            # 创建多个线程同时初始化
            threads = []
            for _ in range(5):
                thread = threading.Thread(target=init_worker)
                threads.append(thread)
                thread.start()

            # 等待所有线程完成
            for thread in threads:
                thread.join()

            # 验证线程安全：所有线程都应该返回 True（第一个初始化，其他的发现已初始化）
            assert all(results)  # 所有线程都返回 True
            assert len(results) == 5  # 5个线程都完成了
            assert init_count == 1  # 只调用了一次初始化
            assert initializer._initialized is True

    def test_lazy_initialization_behavior(self):
        """测试懒加载初始化行为"""
        initializer = AsyncComponentInitializer()

        # 初始状态
        assert not initializer._initialized
        assert initializer.get_executor() is None
        assert initializer.get_async_properties() is None

        # 模拟首次调用
        mock_properties = AsyncProperties(enabled=True)

        def mock_load_properties():
            initializer._async_properties = mock_properties

        with (
            patch.object(initializer, "_load_async_properties", side_effect=mock_load_properties),
            patch("miniboot.asyncs.executor.ThreadPoolTaskExecutor") as mock_executor_class,
        ):
            mock_executor = Mock()
            mock_executor_class.return_value = mock_executor

            # 第一次调用应该触发初始化
            result1 = initializer.initialize_if_needed()
            assert result1 is True
            assert initializer._initialized is True

            # 第二次调用应该直接返回
            result2 = initializer.initialize_if_needed()
            assert result2 is True

            # 验证只创建了一次执行器
            mock_executor_class.assert_called_once()

    def test_disabled_async_behavior(self):
        """测试禁用异步功能的行为"""
        initializer = AsyncComponentInitializer()

        # 模拟禁用配置
        mock_properties = AsyncProperties(enabled=False)

        with patch.object(initializer, "_load_async_properties", return_value=mock_properties):
            result = initializer.initialize_if_needed()

            assert result is False
            assert not initializer._initialized
            assert initializer.get_executor() is None
            assert initializer.get_async_properties() is None

    def test_configuration_validation_error(self):
        """测试配置验证错误"""
        initializer = AsyncComponentInitializer()



        # 模拟环境配置返回无效配置
        mock_environment = Mock()
        mock_environment.get_property.side_effect = lambda key, default=None: {
            "miniboot.async.enabled": True,
            "miniboot.async.default_timeout": -1.0,  # 无效的超时时间
            "miniboot.async.executor.core_size": -1,  # 无效的核心线程数
        }.get(key, default)

        with (
            patch.object(initializer, "_get_environment", return_value=mock_environment),
            patch("miniboot.asyncs.executor.ThreadPoolTaskExecutor") as mock_executor_class,
        ):
            mock_executor = Mock()
            mock_executor_class.return_value = mock_executor

            result = initializer.initialize_if_needed()

            # 配置验证失败时，应该回退到默认配置并成功初始化
            assert result is True
            assert initializer._initialized
            # 验证使用了默认配置
            assert initializer._async_properties.enabled is True
            assert initializer._async_properties.default_timeout is None  # 默认值
            assert initializer._async_properties.executor.core_size == 2  # 默认值

    def test_memory_cleanup_on_failure(self):
        """测试失败时的内存清理"""
        initializer = AsyncComponentInitializer()

        # 模拟初始化失败
        with patch.object(initializer, "_load_async_properties", side_effect=Exception("Load error")):
            result = initializer.initialize_if_needed()

            assert result is False
            assert not initializer._initialized
            assert initializer._executor is None
            assert initializer._async_properties is None


if __name__ == '__main__':
    unittest.main()
